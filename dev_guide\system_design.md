# DCBuyer Web Application System Design Document

## 1. Overview

DCBuyer is a web-based commodity trading and purchasing management system built using CodeIgniter 4 framework with Supabase PostgreSQL database. The system provides role-based access control, comprehensive transaction management, and robust reporting capabilities for agricultural marketplaces.

### 1.1 Purpose
- Streamline commodity purchasing processes through web-based interface
- Provide role-based access control with session-based authentication
- Enable efficient transaction processing and management
- Maintain comprehensive audit trails and transaction records
- Support multi-user collaboration with role-based permissions

### 1.2 Scope
The system covers user management, customer management, commodity management, buyer assignments, and transaction processing using a traditional MVC web application architecture with CodeIgniter 4 and Supabase PostgreSQL.

### 1.3 Architecture Philosophy
- **Web-First**: Traditional web application accessible via browsers
- **MVC Architecture**: Clean separation of concerns using CodeIgniter 4 pattern
- **Database-Centric**: Centralized data storage with Supabase PostgreSQL
- **Session-Based Auth**: Secure server-side session management
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5

## 2. System Architecture

### 2.1 High-Level Architecture (CodeIgniter 4 + Supabase)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile Web    │    │   Admin Panel   │
│   (Desktop)     │    │   (Responsive)  │    │   (Bootstrap)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │ HTTP/HTTPS
         ┌─────────────────────────────────────────────────┐
         │           CodeIgniter 4 Web Application         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Controllers  │  │   Models    │  │  Views   ││
         │  │(Business    │  │(Data Layer) │  │(UI Layer)││
         │  │ Logic)      │  │             │  │          ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   Routes    │  │ Libraries   │  │ Helpers  ││
         │  │(URL Mapping)│  │(Services)   │  │(Utilities)││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │            Supabase PostgreSQL Database         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │    Users    │  │  Customers  │  │Commodities││
         │  │   Table     │  │   Table     │  │  Table   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Transactions │  │Assignments  │  │ Reports  ││
         │  │   Table     │  │   Table     │  │  Table   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Backend Framework:**
- **CodeIgniter 4**: PHP 8.1+ MVC framework
- **PHP**: Server-side scripting language (version 8.1+)
- **Apache/Nginx**: Web server for hosting
- **Composer**: PHP dependency management

**Database:**
- **Supabase PostgreSQL**: Cloud-hosted PostgreSQL database
- **PostgreSQL**: Relational database management system
- **Database Migrations**: CodeIgniter 4 migration system
- **Query Builder**: CodeIgniter 4 database abstraction layer

**Frontend:**
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with Bootstrap 5 framework
- **JavaScript**: Client-side interactivity (ES6+)
- **Bootstrap 5**: Responsive CSS framework
- **jQuery**: JavaScript library for DOM manipulation

**Authentication & Security:**
- **CodeIgniter Sessions**: Server-side session management
- **Password Hashing**: PHP password_hash() function
- **CSRF Protection**: CodeIgniter 4 built-in CSRF protection
- **Input Validation**: Server-side form validation
- **SQL Injection Prevention**: Query builder and prepared statements

**Development Tools:**
- **XAMPP**: Local development environment
- **Composer**: PHP package manager
- **Git**: Version control system
- **VS Code**: Code editor with PHP extensions
- **Browser DevTools**: Debugging and testing

**Deployment:**
- **Web Hosting**: Traditional web hosting with PHP support
- **Database Hosting**: Supabase cloud PostgreSQL
- **File Storage**: Server file system or cloud storage
- **SSL/TLS**: HTTPS encryption for security

## 3. Supabase PostgreSQL Database Design

### 3.1 Database Schema Structure
```
dcbuyers_db (PostgreSQL)
├── users
│   ├── id (bigint, primary key)
│   ├── fullname (text)
│   ├── username (text, unique)
│   ├── email (text, unique)
│   ├── password_hash (varchar)
│   ├── is_admin (boolean)
│   ├── is_buyer (boolean)
│   ├── is_supervisor (boolean)
│   ├── reports_to (bigint, foreign key)
│   ├── status (user_status enum)
│   ├── remarks (text)
│   ├── created_by (bigint)
│   ├── created_at (timestamp)
│   ├── updated_by (bigint)
│   ├── updated_at (timestamp)
│   ├── deleted_by (bigint)
│   └── deleted_at (timestamp)
├── customers (planned)
├── commodities (planned)
├── assignments (planned)
├── transactions (planned)
└── reports (planned)
```

### 3.2 Database Table Schemas

#### Users Table
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    fullname TEXT NOT NULL,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    is_buyer BOOLEAN NOT NULL DEFAULT FALSE,
    is_supervisor BOOLEAN NOT NULL DEFAULT FALSE,
    reports_to BIGINT REFERENCES users(id),
    status user_status NOT NULL DEFAULT 'active',
    remarks TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User status enum
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending');

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_reports_to ON users(reports_to);
```

#### Customers Table (Planned)
```sql
CREATE TABLE customers (
    id BIGSERIAL PRIMARY KEY,
    unique_id VARCHAR(50) NOT NULL UNIQUE,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    credit_limit DECIMAL(15, 2) DEFAULT 0,
    payment_terms VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_customers_unique_id ON customers(unique_id);
CREATE INDEX idx_customers_name ON customers(name);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_location ON customers USING GIST (point(longitude, latitude));
```

#### Commodities Table (Planned)
```sql
CREATE TABLE commodities (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category VARCHAR(100),
    unit_of_measurement VARCHAR(50) NOT NULL,
    base_price DECIMAL(12, 4),
    currency VARCHAR(10) DEFAULT 'USD',
    grade VARCHAR(20),
    specifications JSONB,
    available_quantity DECIMAL(12, 4) DEFAULT 0,
    reserved_quantity DECIMAL(12, 4) DEFAULT 0,
    minimum_order DECIMAL(12, 4) DEFAULT 0,
    maximum_order DECIMAL(12, 4),
    seasonality VARCHAR(100),
    storage_requirements TEXT,
    quality_standards TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_available BOOLEAN NOT NULL DEFAULT TRUE,
    image_url TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_commodities_name ON commodities(name);
CREATE INDEX idx_commodities_category ON commodities(category);
CREATE INDEX idx_commodities_status ON commodities(status);
CREATE INDEX idx_commodities_available ON commodities(is_available);
```

#### Buyer Commodity Assignments Table (Planned)
```sql
CREATE TABLE buyer_commodity_assignments (
    id BIGSERIAL PRIMARY KEY,
    buyer_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    commodity_id BIGINT NOT NULL REFERENCES commodities(id) ON DELETE CASCADE,

    -- Assignment details
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by BIGINT NOT NULL REFERENCES users(id),
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    territory VARCHAR(100),

    -- Purchase limits
    daily_purchase_limit DECIMAL(15, 2),
    weekly_purchase_limit DECIMAL(15, 2),
    monthly_purchase_limit DECIMAL(15, 2),
    max_transaction_amount DECIMAL(15, 2),
    min_transaction_amount DECIMAL(15, 2),

    -- Performance tracking
    total_purchases_amount DECIMAL(15, 2) DEFAULT 0,
    total_transactions_count INTEGER DEFAULT 0,
    last_transaction_date TIMESTAMP WITH TIME ZONE,

    -- Metadata
    notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    UNIQUE(buyer_id, commodity_id, effective_from)
);

-- Indexes
CREATE INDEX idx_assignments_buyer_id ON buyer_commodity_assignments(buyer_id);
CREATE INDEX idx_assignments_commodity_id ON buyer_commodity_assignments(commodity_id);
CREATE INDEX idx_assignments_active ON buyer_commodity_assignments(is_active);
CREATE INDEX idx_assignments_effective ON buyer_commodity_assignments(effective_from, effective_to);
```

#### Transactions Table (Planned)
```sql
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- Parties involved
    buyer_id BIGINT NOT NULL REFERENCES users(id),
    customer_id BIGINT NOT NULL REFERENCES customers(id),
    commodity_id BIGINT NOT NULL REFERENCES commodities(id),

    -- Transaction details
    quantity DECIMAL(12, 4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(12, 4) NOT NULL,
    subtotal DECIMAL(15, 2) NOT NULL,
    tax_amount DECIMAL(15, 2) DEFAULT 0,
    discount_amount DECIMAL(15, 2) DEFAULT 0,
    total_amount DECIMAL(15, 2) NOT NULL,

    -- Payment information
    amount_paid DECIMAL(15, 2) NOT NULL,
    balance_due DECIMAL(15, 2) DEFAULT 0,
    payment_method VARCHAR(30),
    payment_reference VARCHAR(100),
    payment_status VARCHAR(20) DEFAULT 'completed',

    -- Quality and grading
    quality_grade VARCHAR(10),
    quality_notes TEXT,
    moisture_content DECIMAL(5, 2),
    purity_percentage DECIMAL(5, 2),
    quality_checked_by BIGINT REFERENCES users(id),
    quality_checked_at TIMESTAMP WITH TIME ZONE,

    -- Location and timing
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_name VARCHAR(200),

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft',
    approval_status VARCHAR(20) DEFAULT 'pending',
    approved_by BIGINT REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,

    -- Additional fields
    notes TEXT,
    internal_notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- Constraints
    CONSTRAINT transactions_quantity_positive CHECK (quantity > 0),
    CONSTRAINT transactions_unit_price_positive CHECK (unit_price > 0),
    CONSTRAINT transactions_total_positive CHECK (total_amount > 0)
);

-- Indexes
CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_buyer_id ON transactions(buyer_id);
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_commodity_id ON transactions(commodity_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_status ON transactions(status);
```



#### Supporting Tables (Planned)

##### File Attachments Table
```sql
CREATE TABLE file_attachments (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL, -- transaction, customer, commodity, user
    entity_id BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- receipt, photo, document, signature
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT,

    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    uploaded_by BIGINT NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_file_attachments_entity ON file_attachments(entity_type, entity_id);
CREATE INDEX idx_file_attachments_type ON file_attachments(file_type);
CREATE INDEX idx_file_attachments_uploaded_by ON file_attachments(uploaded_by);
```

##### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id BIGINT NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[], -- Array of changed field names

    -- Actor information
    user_id BIGINT REFERENCES users(id),
    user_email VARCHAR(100),
    user_role VARCHAR(50),

    -- Request context
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),

    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Additional context
    reason TEXT, -- Why the change was made
    notes TEXT
);

CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## 4. CodeIgniter 4 System Components

### 4.1 Authentication & Authorization Service
**Technology**: CodeIgniter 4 Sessions + PHP
- **Session-based authentication** with secure server-side sessions
- **Role-based access control (RBAC)** with database-driven permissions
- **Multi-role support** per user with boolean flags (is_admin, is_buyer, is_supervisor)
- **Session management** with CodeIgniter 4 session library
- **Password security** with PHP password_hash() and password_verify()
- **CSRF protection** with CodeIgniter 4 built-in CSRF tokens
- **Input validation** with CodeIgniter 4 validation library

### 4.2 User Management Service
**Technology**: CodeIgniter 4 Models + Controllers
- **CRUD operations** for users with server-side validation
- **Role assignment/management** with database updates
- **User profile management** with file upload capabilities
- **Activity logging** with audit trail functionality
- **User search and filtering** with database queries
- **Hierarchical user structure** with reports_to relationships

### 4.3 Customer Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Customer registration** with server-side validation
- **Unique customer ID generation** with database sequences
- **Contact information management** with relational database
- **Geographic location tracking** with latitude/longitude fields
- **Document management** with file upload system
- **Customer search** with SQL queries and indexing
- **Credit limit management** and payment terms tracking

### 4.4 Commodity Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Commodity catalog management** with database tables
- **Unit of measurement** standardization
- **Dynamic pricing** with database updates
- **Quality specifications** with JSONB fields
- **Seasonal availability** tracking with date fields
- **Image gallery management** with file system storage
- **Inventory tracking** with database counters

### 4.5 Assignment Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Buyer-commodity assignment** with relational tables
- **Assignment history tracking** with audit trails
- **Bulk assignment operations** with database transactions
- **Purchase limit management** with validation rules
- **Performance tracking** with aggregated queries
- **Assignment validation** with business logic

### 4.6 Transaction Processing Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Transaction creation** with form-based input
- **Multi-step workflow** using database status fields
- **Payment processing** with server-side calculations
- **Quality assessment** integration with file uploads
- **Receipt generation** with PDF libraries
- **Transaction approval** workflow with role-based permissions
- **Commission calculation** with database triggers

### 4.7 File Management Service (Planned)
**Technology**: CodeIgniter 4 + File System
- **File upload** with server-side validation and processing
- **Image optimization** with PHP image libraries
- **Secure file access** with authentication checks
- **File organization** by entity type and ID
- **Metadata management** with database file records
- **File cleanup** with scheduled maintenance tasks

### 4.8 Reporting & Analytics Service (Planned)
**Technology**: CodeIgniter 4 + Supabase + Chart Libraries
- **Transaction reports** with SQL aggregation queries
- **Performance analytics** with database analytics
- **Financial reports** with server-side calculations
- **Data export** with PHP libraries (TCPDF, PhpSpreadsheet)
- **Dashboard metrics** with cached database queries
- **Custom report builder** with dynamic SQL generation

## 5. CodeIgniter 4 Routing & URL Design

### 5.1 Web Application Architecture Principles
- **MVC pattern** with clear separation of concerns
- **Resource-based URLs** with CodeIgniter 4 routing
- **Form-based interactions** with POST/GET methods
- **Session-based authentication** with server-side validation
- **Semantic URLs** with user-friendly naming
- **CSRF protection** for all form submissions
- **Input validation** with server-side rules
- **Error handling** with user-friendly messages

### 5.2 Core Application Routes

#### Authentication Routes
```
GET    /                               # Landing page with login form
POST   /auth/login                     # Process user login
GET    /auth/logout                    # User logout
GET    /dashboard                      # Main dashboard (authenticated)
```

#### User Management Routes (Admin)
```
GET    /admin/users                    # List all users
GET    /admin/users/create             # Show create user form
POST   /admin/users/create             # Process create user
GET    /admin/users/(:num)             # View user details
GET    /admin/users/(:num)/edit        # Show edit user form
POST   /admin/users/(:num)/edit        # Process user update
POST   /admin/users/(:num)/delete      # Deactivate user
POST   /admin/users/(:num)/activate    # Activate user
```

#### Customer Management Routes (Planned)
```
GET    /customers                      # List customers
GET    /customers/create               # Show create customer form
POST   /customers/create               # Process create customer
GET    /customers/(:num)               # View customer details
GET    /customers/(:num)/edit          # Show edit customer form
POST   /customers/(:num)/edit          # Process customer update
POST   /customers/(:num)/delete        # Deactivate customer
GET    /customers/search               # Search customers
```

#### Commodity Management Routes (Planned)
```
GET    /commodities                    # List commodities
GET    /commodities/create             # Show create commodity form
POST   /commodities/create             # Process create commodity
GET    /commodities/(:num)             # View commodity details
GET    /commodities/(:num)/edit        # Show edit commodity form
POST   /commodities/(:num)/edit        # Process commodity update
POST   /commodities/(:num)/delete      # Deactivate commodity
```

#### Assignment Management Routes (Planned)
```
GET    /assignments                    # List buyer assignments
GET    /assignments/create             # Show create assignment form
POST   /assignments/create             # Process create assignment
GET    /assignments/(:num)             # View assignment details
GET    /assignments/(:num)/edit        # Show edit assignment form
POST   /assignments/(:num)/edit        # Process assignment update
POST   /assignments/(:num)/delete      # Deactivate assignment
```

#### Transaction Management Routes (Planned)
```
GET    /transactions                   # List transactions
GET    /transactions/create            # Show create transaction form
POST   /transactions/create            # Process create transaction
GET    /transactions/(:num)            # View transaction details
GET    /transactions/(:num)/edit       # Show edit transaction form
POST   /transactions/(:num)/edit       # Process transaction update
POST   /transactions/(:num)/approve    # Approve transaction
POST   /transactions/(:num)/reject     # Reject transaction
GET    /transactions/(:num)/receipt    # Generate receipt PDF
```

#### Reporting Routes (Planned)
```
GET    /reports                        # Reports dashboard
GET    /reports/transactions           # Transaction reports
GET    /reports/buyers                 # Buyer performance reports
GET    /reports/commodities            # Commodity reports
GET    /reports/export/(:segment)      # Export reports (PDF/Excel)
```

### 5.3 Form Handling & Validation
**Server-Side Validation**:
- CodeIgniter 4 validation library for all form inputs
- CSRF protection on all POST requests
- Input sanitization and filtering
- Custom validation rules for business logic

**Response Handling**:
- Redirect with flash messages for success/error states
- Form repopulation on validation errors
- User-friendly error messages
- Success confirmations with appropriate redirects

**Data Pagination**:
- CodeIgniter 4 pagination library for large datasets
- Configurable page sizes
- SEO-friendly pagination URLs
- Previous/Next navigation controls

## 6. User Workflows

### 6.1 Buyer Workflow (Planned)
1. **Login** → Session-based authentication
2. **View Dashboard** → Display assigned commodities and recent transactions
3. **Select Commodity** → Choose from assigned commodity list
4. **Select Customer** → Choose existing or create new customer
5. **Enter Transaction Details** → Quantity, price, payment method
6. **Submit Transaction** → Server-side validation and database storage
7. **Generate Receipt** → PDF receipt generation and download

### 6.2 Admin Workflow
1. **Login** → Session-based authentication with admin privileges
2. **User Management** → Create, edit, and manage user accounts
3. **Role Assignment** → Assign buyer, supervisor, admin roles
4. **System Configuration** → Manage application settings
5. **Reports & Analytics** → View system-wide reports and statistics

### 6.3 Supervisor Workflow (Planned)
1. **Login** → Session-based authentication with supervisor privileges
2. **Transaction Monitoring** → View and approve buyer transactions
3. **Performance Reports** → Analyze buyer and commodity performance
### 6.4 Evaluator Workflow (Planned)
1. **Login** → Session-based authentication with evaluator privileges
2. **Quality Assessment** → Record commodity quality and grading
3. **Price Validation** → Verify and approve transaction prices
4. **Audit Reports** → Generate quality and compliance audit trails

## 7. Security Considerations

### 7.1 Authentication Security
- **Password hashing** with PHP password_hash() (bcrypt/argon2)
- **Session management** with CodeIgniter 4 session library
- **Session regeneration** on login to prevent session fixation
- **Account status validation** (active/inactive/suspended)
- **Login attempt monitoring** and rate limiting

### 7.2 Data Security
- **HTTPS enforcement** for all production traffic
- **Database encryption** at rest with Supabase
- **Input validation** with CodeIgniter 4 validation library
- **SQL injection prevention** with query builder and prepared statements
- **XSS protection** with output escaping and CSP headers

### 7.3 Access Control
- **Role-based permissions** with database-driven roles
- **Route-level authorization** with CodeIgniter 4 filters
- **CSRF protection** on all form submissions
- **Session timeout** and automatic logout
- **Audit logging** for sensitive operations

## 8. Performance Considerations

### 8.1 Database Optimization
- **Proper indexing strategy** for frequently queried columns
- **Query optimization** with CodeIgniter 4 query builder
- **Connection pooling** with Supabase PostgreSQL
- **Pagination** for large result sets
- **Database monitoring** with Supabase dashboard

### 8.2 Application Performance
- **CodeIgniter 4 caching** for frequently accessed data
- **View caching** for static content
- **Gzip compression** for HTTP responses
- **Asset minification** for CSS/JavaScript files
- **Image optimization** for uploaded files

### 8.3 Web Performance
- **Responsive design** for mobile compatibility
- **Lazy loading** for images and content
- **Browser caching** with appropriate headers
- **CDN integration** for static assets (if needed)

## 9. Monitoring & Analytics

### 9.1 System Monitoring
- **Application logging** with CodeIgniter 4 logging library
- **Database monitoring** with Supabase dashboard and metrics
- **Error tracking** with custom error handlers
- **Performance monitoring** with server metrics
- **Security monitoring** for failed login attempts

### 9.2 Business Analytics (Planned)
- **Transaction reporting** with database queries
- **User activity tracking** with audit logs
- **Commodity performance metrics** with aggregated data
- **Revenue reporting** with financial calculations
- **Dashboard metrics** with real-time statistics

## 10. Deployment Architecture

### 10.1 Environment Strategy
- **Development** → XAMPP local environment with Supabase
- **Staging** → Shared hosting or VPS for testing
- **Production** → Web hosting with PHP 8.1+ and SSL

### 10.2 Infrastructure Requirements
- **Web Server** → Apache or Nginx with PHP 8.1+
- **Database** → Supabase PostgreSQL (cloud-hosted)
- **SSL Certificate** → HTTPS encryption for security
- **File Storage** → Server file system or cloud storage
- **Backup Strategy** → Database backups and file backups

## 11. File Management Implementation (Planned)

### 11.1 File Storage Structure
```
dcbuyer-files/
├── uploads/
│   ├── profiles/
│   │   └── {userId}/
│   │       └── avatar.jpg
│   ├── transactions/
│   │   └── {transactionId}/
│   │       ├── receipts/
│   │       └── photos/
│   ├── commodities/
│   │   └── {commodityId}/
│   │       └── images/
│   └── customers/
│       └── {customerId}/
│           └── documents/
├── templates/
│   ├── receipt_template.pdf
│   └── report_templates/
└── exports/
    ├── reports/
    └── backups/
```

### 11.2 File Upload Service (CodeIgniter 4)
```php
<?php
class FileUploadService
{
    protected $uploadPath = WRITEPATH . 'uploads/';

    public function uploadFile($file, $entityType, $entityId)
    {
        $validation = \Config\Services::validation();
        $validation->setRules([
            'file' => 'uploaded[file]|max_size[file,2048]|ext_in[file,jpg,jpeg,png,pdf]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return false;
        }

        $newName = $file->getRandomName();
        $path = $entityType . '/' . $entityId . '/';

        if ($file->move($this->uploadPath . $path, $newName)) {
            return $path . $newName;
        }

        return false;
    }
}
```

### 11.3 File Security and Access Control
- **Upload validation** with file type and size restrictions
- **Secure file paths** outside web root directory
- **Access control** with authentication checks
- **File scanning** for malware (if implemented)
- **Backup strategy** for uploaded files

## 12. Future Enhancements

### 12.1 Planned Features
- **Customer Management Module** - Complete CRUD operations for customers
- **Commodity Management Module** - Catalog management with pricing
- **Transaction Processing Module** - Full transaction workflow
- **Assignment Management Module** - Buyer-commodity assignments
- **Reporting Dashboard** - Analytics and business intelligence
- **Mobile-Responsive Interface** - Enhanced mobile experience
- **Email Notifications** - Transaction and system notifications
- **PDF Report Generation** - Automated report creation
- **Data Export/Import** - Excel/CSV data handling
- **Advanced Search** - Full-text search capabilities

### 12.2 Technical Enhancements
- **API Development** - RESTful API for mobile app integration
- **Caching Implementation** - Redis or Memcached for performance
- **Queue System** - Background job processing
- **Multi-language Support** - Internationalization (i18n)
- **Advanced Security** - Two-factor authentication, audit trails
- **Performance Optimization** - Database query optimization
- **Automated Testing** - Unit and integration tests
- **CI/CD Pipeline** - Automated deployment process

### 12.3 Scalability Considerations
- **Load Balancing** - Multiple server instances
- **Database Optimization** - Read replicas and partitioning
- **CDN Integration** - Content delivery network for assets
- **Microservices Migration** - Service-oriented architecture (future)

---

*This document serves as the foundation for the DCBuyer web application development using CodeIgniter 4 and Supabase PostgreSQL. It should be updated as requirements evolve and new features are implemented.*
